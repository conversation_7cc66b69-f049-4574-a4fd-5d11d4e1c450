const { spawn } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const net = require('net');
const path = require('path');

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run-fp16.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  console.log('[Cleanup] Checking for lingering Python processes...');

  return new Promise((resolve) => {
    // First, check if there are any Python processes running
    const checkProcess = spawn('cmd.exe', ['/c', 'tasklist | findstr python.exe'], {
      stdio: 'pipe',
      windowsHide: true
    });

    let output = '';
    checkProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    checkProcess.on('close', (code) => {
      if (output.trim() && output.includes('python.exe')) {
        console.log('[Cleanup] Found Python processes, killing them...');
        console.log('[Cleanup] Processes found:', output.trim());

        // Kill all Python processes
        const killProcess = spawn('cmd.exe', ['/c', 'taskkill /F /IM python.exe'], {
          stdio: 'pipe',
          windowsHide: true
        });

        killProcess.on('close', (killCode) => {
          if (killCode === 0) {
            console.log('[Cleanup] Successfully killed Python processes');
          } else {
            console.log('[Cleanup] Some Python processes may still be running (exit code:', killCode, ')');
          }

          // Wait a moment for processes to fully terminate
          setTimeout(() => {
            resolve();
          }, 2000);
        });

        killProcess.on('error', (err) => {
          console.warn('[Cleanup] Error killing Python processes:', err.message);
          resolve();
        });
      } else {
        console.log('[Cleanup] No Python processes found');
        resolve();
      }
    });

    checkProcess.on('error', (err) => {
      console.warn('[Cleanup] Error checking for Python processes:', err.message);
      resolve();
    });
  });
}

function isTrellisRunning() {
  // Only log every 10th check to reduce spam
  const shouldLogCheck = connectionAttemptCounter % 10 === 0;
  if (shouldLogCheck) {
    console.log('[Trellis Server] Checking if server is running on', TRELLIS_HOST + ':' + TRELLIS_PORT);
  }

  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => {
      console.log('[Trellis Server] Server is running - connection successful');
      connectionAttemptCounter = 0; // Reset counter on successful connection
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      connectionAttemptCounter++;
      // Only log every 10th connection failure to reduce spam
      if (connectionAttemptCounter % 10 === 0) {
        console.log(`[Trellis Server] Server not running - connection failed (${connectionAttemptCounter} attempts):`, error.code);
      }
      resolve(false);
    });
  });
}

function startTrellisServer(progressCb = null) {
  console.log('[Trellis Server] Starting server...');
  console.log('[Trellis Server] RUN_BAT:', RUN_BAT);
  console.log('[Trellis Server] Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run-fp16.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  console.log('[Trellis Server] Working directory:', batDir);
  console.log('[Trellis Server] Command: cmd.exe /c', path.basename(RUN_BAT));

  // Store the progress callback globally so it can be used during generation
  globalProgressCallback = progressCb;

  // Use cmd.exe with specific flags to prevent window showing
  const child = spawn('cmd.exe', ['/c', path.basename(RUN_BAT)], {
    cwd: batDir,
    detached: false,  // Changed to false so we can capture output
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    env: {
      ...process.env,
      // Set environment variables to minimize window visibility
      PYTHONUNBUFFERED: '1'
    }
  });
  // Capture and display selective server output in main application logs
  child.stdout.on('data', (data) => {
    const output = data.toString();
    const lines = output.split('\n').filter(line => line.trim());

    lines.forEach(line => {
      const trimmedLine = line.trim();

      // Only log important lines, not every progress update
      const shouldLog =
        trimmedLine.includes('INFO') ||
        trimmedLine.includes('ERROR') ||
        trimmedLine.includes('WARNING') ||
        trimmedLine.includes('Starting') ||
        trimmedLine.includes('Completed') ||
        trimmedLine.includes('Failed') ||
        trimmedLine.includes('Loading') ||
        trimmedLine.includes('Initializing') ||
        trimmedLine.includes('Client asked to generate') ||
        trimmedLine.includes('Decoding the SLAT') ||
        trimmedLine.includes('GLB saved to') ||
        trimmedLine.includes('Generation completed') ||
        trimmedLine.includes('error while attempting to bind') ||
        trimmedLine.includes('Address already in use') ||
        // Log every 10th progress line for texture baking (most verbose)
        (trimmedLine.includes('Texture baking') && shouldLogProgressLine(trimmedLine, 10)) ||
        // Log every 5th progress line for other operations
        (trimmedLine.includes('Sampling:') && shouldLogProgressLine(trimmedLine, 5)) ||
        (trimmedLine.includes('Decimating') && shouldLogProgressLine(trimmedLine, 5)) ||
        (trimmedLine.includes('Rendering:') && shouldLogProgressLine(trimmedLine, 5)) ||
        (trimmedLine.includes('Rasterizing:') && shouldLogProgressLine(trimmedLine, 5));

      if (shouldLog) {
        console.log('[Trellis Server] ' + trimmedLine);
        if (typeof logger !== 'undefined') logger.info('[Trellis Server] ' + trimmedLine);
      }

      // Check for port binding errors
      if (trimmedLine.includes('error while attempting to bind') || trimmedLine.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });

    // Parse progress from server output if we have a callback (but don't log every parse attempt)
    if (globalProgressCallback) {
      parseProgressFromOutput(output, globalProgressCallback);
    }
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      const trimmedLine = line.trim();

      // Only log important error messages, not every stderr line
      const shouldLogError =
        trimmedLine.includes('ERROR') ||
        trimmedLine.includes('CRITICAL') ||
        trimmedLine.includes('FATAL') ||
        trimmedLine.includes('Failed') ||
        trimmedLine.includes('Exception') ||
        trimmedLine.includes('Traceback') ||
        trimmedLine.includes('error while attempting to bind') ||
        trimmedLine.includes('Address already in use');

      if (shouldLogError) {
        console.error('[Trellis Server] ' + trimmedLine);
        if (typeof logger !== 'undefined') logger.error('[Trellis Server] ' + trimmedLine);
      }

      // Check for port binding errors in stderr too
      if (trimmedLine.includes('error while attempting to bind') || trimmedLine.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });
  // Handle process events and log them to the main application logs
  child.on('close', (code) => {
    console.log(`[Trellis Server] Process exited with code ${code}`);
    if (typeof logger !== 'undefined') logger.info(`[Trellis Server] Process exited with code ${code}`);
  });

  child.on('error', (error) => {
    console.error('[Trellis Server] Process error:', error);
    if (typeof logger !== 'undefined') logger.error('[Trellis Server] Process error: ' + error.message);
  });

  // Store the child process reference globally so we can check if it's still running
  global.trellisProcess = child;
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  let waitCounter = 0;
  while (true) {
    if (await isTrellisRunning()) return true;

    waitCounter++;
    // Only send progress callback every 5th check (every 10 seconds) to reduce spam
    if (progressCb && waitCounter % 5 === 0) {
      progressCb({ stage: 'trellis', message: 'Waiting for Trellis server to start...' });
    }

    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever
  }
}

// Global progress callback for server output parsing
let globalProgressCallback = null;

// Track current stage for simplified progress tracking
let currentStage = 'preprocessing';
let stageCompleted = {
  preprocessing: false,
  sparse_structure: false,
  slat_generation: false,
  mesh_creation: false,
  texture_generation: false,
  glb_export: false
};

// Counter for progress line logging to reduce spam
let progressLineCounter = 0;

// Helper function to determine if we should log this progress line
function shouldLogProgressLine(line, interval = 10) {
  progressLineCounter++;
  return progressLineCounter % interval === 0;
}

// Reset stage tracking for new generation
function resetStageTracking() {
  currentStage = 'preprocessing';
  stageCompleted = {
    preprocessing: false,
    sparse_structure: false,
    slat_generation: false,
    mesh_creation: false,
    texture_generation: false,
    glb_export: false
  };
  progressLineCounter = 0; // Reset progress line counter
  console.log('[Trellis Progress] Stage tracking reset for new generation');
}

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    return;
  }

  const lines = output.split('\n');
  for (const line of lines) {
    try {
      const cleanLine = line.trim();
      if (!cleanLine) continue;

      // Skip ECONNREFUSED messages as they're not actual errors when server runs fine
      if (cleanLine.includes('ECONNREFUSED')) {
        continue;
      }

      // PREPROCESSING STAGE - Complete when server is ready and processing starts
      if (!stageCompleted.preprocessing) {
        if (cleanLine.includes('Client asked to generate with no previews') ||
            cleanLine.includes('Sampling:') ||
            cleanLine.includes('Loading Trellis pipeline')) {
          console.log('[Trellis Progress] Preprocessing: 100% → Starting sparse structure generation');
          stageCompleted.preprocessing = true;
          currentStage = 'sparse_structure';
          progressCb({ stage: 'preprocessing', progress: 100, message: 'Preprocessing completed' });
          progressCb({ stage: 'sparse_structure', progress: 0, message: 'Starting sparse structure generation...' });
        }
        continue;
      }

      // SPARSE STRUCTURE STAGE - Track progress and complete when finished
      if (!stageCompleted.sparse_structure && currentStage === 'sparse_structure') {
        // Parse sampling progress for sparse structure
        if (cleanLine.includes('Sampling:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/Sampling:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Log every 10% increment
            if (percentage % 10 === 0) {
              console.log(`[Trellis Progress] Sparse Structure: ${percentage}%`);
            }
            progressCb({ stage: 'sparse_structure', progress: percentage, message: `Sparse structure: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Sampling: 100%|##########| 12/12')) {
          console.log('[Trellis Progress] Sparse Structure: 100% → Starting SLAT generation');
          stageCompleted.sparse_structure = true;
          currentStage = 'slat_generation';
          progressCb({ stage: 'sparse_structure', progress: 100, message: 'Sparse structure completed' });
          progressCb({ stage: 'slat_generation', progress: 0, message: 'Starting SLAT generation...' });
        }
        continue;
      }

      // SLAT GENERATION STAGE - Track progress and complete when finished
      if (!stageCompleted.slat_generation && currentStage === 'slat_generation') {
        // Parse sampling progress for SLAT generation
        if (cleanLine.includes('Sampling:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/Sampling:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Log every 10% increment
            if (percentage % 10 === 0) {
              console.log(`[Trellis Progress] SLAT Generation: ${percentage}%`);
            }
            progressCb({ stage: 'slat_generation', progress: percentage, message: `SLAT generation: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Sampling: 100%|##########| 12/12') ||
            cleanLine.includes('Decoding the SLAT, please wait...')) {
          console.log('[Trellis Progress] SLAT Generation: 100% → Starting mesh creation');
          stageCompleted.slat_generation = true;
          currentStage = 'mesh_creation';
          progressCb({ stage: 'slat_generation', progress: 100, message: 'SLAT generation completed' });
          progressCb({ stage: 'mesh_creation', progress: 0, message: 'Starting mesh creation...' });
        }
        continue;
      }

      // MESH CREATION STAGE - Track progress and complete when finished
      if (!stageCompleted.mesh_creation && currentStage === 'mesh_creation') {
        // Parse decimation progress
        if (cleanLine.includes('Decimating Mesh:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/Decimating Mesh:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Log every 10% increment
            if (percentage % 10 === 0) {
              console.log(`[Trellis Progress] Mesh Decimation: ${percentage}%`);
            }
            progressCb({ stage: 'mesh_creation', progress: percentage, message: `Mesh decimation: ${percentage}%` });
          }
        }

        // Parse rendering progress
        if (cleanLine.includes('Rendering:') && cleanLine.includes('it [')) {
          const iterationMatch = cleanLine.match(/Rendering:\s*(\d+)it/);
          if (iterationMatch) {
            const iterations = parseInt(iterationMatch[1]);
            const percentage = Math.min(Math.floor((iterations / 100) * 100), 100);
            // Log every 10% increment
            if (percentage % 10 === 0) {
              console.log(`[Trellis Progress] Mesh Rendering: ${percentage}%`);
            }
            progressCb({ stage: 'mesh_creation', progress: percentage, message: `Mesh rendering: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Decimating Mesh: 100%|##########') ||
            cleanLine.includes('Rendering: 100it [')) {
          console.log('[Trellis Progress] Mesh Creation: 100% → Starting texture generation');
          stageCompleted.mesh_creation = true;
          currentStage = 'texture_generation';
          progressCb({ stage: 'mesh_creation', progress: 100, message: 'Mesh creation completed' });
          progressCb({ stage: 'texture_generation', progress: 0, message: 'Starting texture generation...' });
        }
        continue;
      }

      // TEXTURE GENERATION STAGE - Track progress and complete when finished
      if (!stageCompleted.texture_generation && currentStage === 'texture_generation') {
        // Parse texture baking progress
        if (cleanLine.includes('Texture baking (opt): optimizing:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/optimizing:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Log every 10% increment
            if (percentage % 10 === 0) {
              console.log(`[Trellis Progress] Texture Generation: ${percentage}%`);
            }
            progressCb({ stage: 'texture_generation', progress: percentage, message: `Texture generation: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Texture baking (opt): optimizing: 100%|##########| 2500/2500') ||
            cleanLine.includes('GLB saved to:') ||
            cleanLine.includes('Model saved to:')) {
          console.log('[Trellis Progress] Texture Generation: 100% → Starting GLB export');
          stageCompleted.texture_generation = true;
          currentStage = 'glb_export';
          progressCb({ stage: 'texture_generation', progress: 100, message: 'Texture generation completed' });
          progressCb({ stage: 'glb_export', progress: 0, message: 'Starting GLB export...' });
        }
        continue;
      }

      // GLB EXPORT STAGE - Complete when model is fully exported
      if (!stageCompleted.glb_export && currentStage === 'glb_export') {
        if (cleanLine.includes('Generation completed') ||
            cleanLine.includes('Model generation completed') ||
            cleanLine.includes('Videos rendered successfully')) {
          console.log('[Trellis Progress] GLB Export: 100% → Generation finished!');
          stageCompleted.glb_export = true;
          progressCb({ stage: 'glb_export', progress: 100, message: 'GLB export completed' });
        }
        continue;
      }

    } catch (error) {
      console.error(`[Trellis Progress] Error in progress tracking: ${error}`);
    }
  }
}

function mapTrellisMessageToStage(message, progress) {
  // Simple stage mapping based on progress percentage
  // This matches the ProgressBar component's expected stages

  if (progress <= 0) {
    return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  } else if (progress <= 10) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure foundation' };
  } else if (progress <= 50) {
    return { stage: 'slat_generation', description: 'Creating detailed 3D representation' };
  } else if (progress <= 60) {
    return { stage: 'mesh_creation', description: 'Processing and optimizing mesh' };
  } else if (progress <= 95) {
    return { stage: 'glb_export', description: 'Finalizing and exporting 3D model' };
  } else {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }
}

async function generate3DModel(imagePath, progressCb) {
  console.log('[Trellis Server] generate3DModel called with imagePath:', imagePath);

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  const isRunning = await isTrellisRunning();
  console.log('[Trellis Server] isTrellisRunning() returned:', isRunning);

  // Reset stage tracking for new generation
  resetStageTracking();

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    console.log('[Trellis Server] Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startTrellisServer(progressCb);
      console.log('[Trellis Server] startTrellisServer() called successfully');
    } catch (error) {
      console.error('[Trellis Server] Error starting server:', error);
      throw error;
    }
    await waitForTrellisReady(null, progressCb);
  } else {
    console.log('[Trellis Server] Server already running, skipping startup');
  }

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }
  const form = new FormData();
  form.append('file', fs.createReadStream(imagePath), { filename: path.basename(imagePath) });

  let response;
  try {
    console.log('[Trellis Server] Sending POST request to:', `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`);
    console.log('[Trellis Server] Form data keys:', Object.keys(form._streams || {}));

    response = await axios.post(
      `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
      form,
      {
        headers: form.getHeaders(),
        maxBodyLength: Infinity,
        timeout: 300000 // 5 minutes timeout for long-running generation
      }
    );
    console.log('[Trellis Server] POST response status:', response.status);
    console.log('[Trellis Server] POST response data:', response.data);
  } catch (err) {
    console.error('[Trellis Server] POST request failed:', err.response?.status, err.response?.data);
    console.error('[Trellis Server] Full error:', err.message);
    throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
  }

  let status = response.data.status;
  let progress = response.data.progress;
  let message = response.data.message;
  let modelUrl = response.data.model_url;

  // Emit initial progress
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage(message, progress);
    progressCb({ stage, progress, message: description });
  }

  // If still processing, poll for progress using the status endpoint
  while (status === 'PROCESSING') {
    await new Promise(r => setTimeout(r, 2000));
    try {
      const pollResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/status`, { timeout: 30000 });
      status = pollResponse.data.status;
      progress = pollResponse.data.progress;
      message = pollResponse.data.message;
      modelUrl = pollResponse.data.model_url;

      console.log(`[Trellis Server] Progress poll - Status: ${status}, Progress: ${progress}%, Message: ${message}`);

      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage(message, progress);
        console.log(`[Trellis Server] Calling progress callback - Stage: ${stage}, Progress: ${progress}%, Description: ${description}`);
        progressCb({ stage, progress, message: description });
      } else {
        console.log('[Trellis Server] No progress callback available during polling');
      }
    } catch (err) {
      console.error('[Trellis Server] Error during progress polling:', err.message);
      if (progressCb) progressCb({ stage: 'trellis', progress, message: 'Error polling Trellis progress: ' + err.message });
      break;
    }
  }

  if (!modelUrl) throw new Error('No model_url in Trellis response');

  // Convert relative URL to absolute URL if needed
  const fullModelUrl = modelUrl.startsWith('http') ? modelUrl : `http://${TRELLIS_HOST}:${TRELLIS_PORT}${modelUrl}`;

  console.log('[Trellis Server] Model URL from server:', modelUrl);
  console.log('[Trellis Server] Full model URL for download:', fullModelUrl);

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const modelResponse = await axios.get(fullModelUrl, { responseType: 'stream', timeout: 60000 });
  let fileName = path.basename(fullModelUrl.split('?')[0]) || 'model';

  // Ensure the file has a .glb extension
  if (!fileName.endsWith('.glb') && !fileName.endsWith('.gltf')) {
    fileName = fileName + '.glb';
  }

  const outputPath = path.join(OUTPUT_DIR, fileName);

  console.log('[Trellis Server] Downloading model to:', outputPath);
  console.log('[Trellis Server] Model response status:', modelResponse.status);
  console.log('[Trellis Server] Model response headers:', modelResponse.headers['content-type']);

  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', () => {
      console.log('[Trellis Server] Model file saved successfully');
      resolve();
    });
    writer.on('error', (error) => {
      console.error('[Trellis Server] Error saving model file:', error);
      reject(error);
    });
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Convert absolute path to relative path for the frontend
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);

  console.log('[Trellis Server] Output file absolute path:', outputPath);
  console.log('[Trellis Server] Output file relative path:', relativePath);
  console.log('[Trellis Server] File exists:', fs.existsSync(outputPath));
  if (fs.existsSync(outputPath)) {
    const stats = fs.statSync(outputPath);
    console.log('[Trellis Server] File size:', stats.size, 'bytes');
  }

  return relativePath;
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses
};